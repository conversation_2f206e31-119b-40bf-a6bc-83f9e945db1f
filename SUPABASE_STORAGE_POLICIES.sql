-- Supabase Storage Policies for Secure Image Management
-- Execute these policies in your Supabase SQL Editor

-- =====================================================
-- BUCKET SETUP
-- =====================================================

-- Create driver-licenses bucket (private)
INSERT INTO storage.buckets (id, name, public)
VALUES ('driver-licenses', 'driver-licenses', false)
ON CONFLICT (id) DO UPDATE SET
  name = 'driver-licenses',
  public = false;

-- Create/Update profile-images bucket (public)
INSERT INTO storage.buckets (id, name, public)
VALUES ('profile-images', 'profile-images', true)
ON CONFLICT (id) DO UPDATE SET
  name = 'profile-images',
  public = true;

-- =====================================================
-- PROFILE-IMAGES BUCKET POLICIES (PUBLIC)
-- =====================================================

-- Allow public read access to profile-images
CREATE POLICY "Public read access for profile images"
ON storage.objects
FOR SELECT
USING (bucket_id = 'profile-images');

-- Allow authenticated users to upload their own profile images
CREATE POLICY "Users can upload their own profile images"
ON storage.objects
FOR INSERT
WITH CHECK (
  bucket_id = 'profile-images' 
  AND auth.role() = 'authenticated'
  AND (
    -- Allow uploads to users/ folder with user ID in filename
    (name LIKE 'users/profile_' || auth.uid()::text || '.%')
    OR
    -- Allow uploads to vehicles/ folder with user ID in filename
    (name LIKE 'vehicles/vehicle_' || auth.uid()::text || '.%')
  )
);

-- Allow users to update their own profile images
CREATE POLICY "Users can update their own profile images"
ON storage.objects
FOR UPDATE
USING (
  bucket_id = 'profile-images' 
  AND auth.role() = 'authenticated'
  AND (
    -- Allow updates to users/ folder with user ID in filename
    (name LIKE 'users/profile_' || auth.uid()::text || '.%')
    OR
    -- Allow updates to vehicles/ folder with user ID in filename
    (name LIKE 'vehicles/vehicle_' || auth.uid()::text || '.%')
  )
);

-- Allow users to delete their own profile images
CREATE POLICY "Users can delete their own profile images"
ON storage.objects
FOR DELETE
USING (
  bucket_id = 'profile-images' 
  AND auth.role() = 'authenticated'
  AND (
    -- Allow deletes from users/ folder with user ID in filename
    (name LIKE 'users/profile_' || auth.uid()::text || '.%')
    OR
    -- Allow deletes from vehicles/ folder with user ID in filename
    (name LIKE 'vehicles/vehicle_' || auth.uid()::text || '.%')
  )
);

-- =====================================================
-- DRIVER-LICENSES BUCKET POLICIES (PRIVATE)
-- =====================================================

-- Allow users to upload their own license documents
CREATE POLICY "Users can upload their own license documents"
ON storage.objects
FOR INSERT
WITH CHECK (
  bucket_id = 'driver-licenses' 
  AND auth.role() = 'authenticated'
  AND name LIKE 'license_' || auth.uid()::text || '.%'
);

-- Allow users to read their own license documents
CREATE POLICY "Users can read their own license documents"
ON storage.objects
FOR SELECT
USING (
  bucket_id = 'driver-licenses' 
  AND auth.role() = 'authenticated'
  AND name LIKE 'license_' || auth.uid()::text || '.%'
);

-- Allow users to update their own license documents
CREATE POLICY "Users can update their own license documents"
ON storage.objects
FOR UPDATE
USING (
  bucket_id = 'driver-licenses' 
  AND auth.role() = 'authenticated'
  AND name LIKE 'license_' || auth.uid()::text || '.%'
);

-- Allow users to delete their own license documents
CREATE POLICY "Users can delete their own license documents"
ON storage.objects
FOR DELETE
USING (
  bucket_id = 'driver-licenses' 
  AND auth.role() = 'authenticated'
  AND name LIKE 'license_' || auth.uid()::text || '.%'
);

-- Allow admins to read all license documents (optional)
CREATE POLICY "Admins can read all license documents"
ON storage.objects
FOR SELECT
USING (
  bucket_id = 'driver-licenses' 
  AND auth.role() = 'authenticated'
  AND EXISTS (
    SELECT 1 FROM public.users 
    WHERE id = auth.uid() 
    AND role = 'admin'
  )
);

-- =====================================================
-- DATABASE TABLE POLICIES
-- =====================================================

-- Update users table policy for profile images
CREATE POLICY "Users can update their own profile"
ON public.users
FOR UPDATE
USING (auth.uid() = id)
WITH CHECK (auth.uid() = id);

-- Driver licenses table policies
CREATE POLICY "Users can insert their own license data"
ON public.driver_licenses
FOR INSERT
WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can read their own license data"
ON public.driver_licenses
FOR SELECT
USING (auth.uid() = user_id);

CREATE POLICY "Users can update their own license data"
ON public.driver_licenses
FOR UPDATE
USING (auth.uid() = user_id)
WITH CHECK (auth.uid() = user_id);

-- Vehicles table policies
CREATE POLICY "Users can insert their own vehicle data"
ON public.vehicles
FOR INSERT
WITH CHECK (auth.uid() = owner_id);

CREATE POLICY "Users can read their own vehicle data"
ON public.vehicles
FOR SELECT
USING (auth.uid() = owner_id);

CREATE POLICY "Users can update their own vehicle data"
ON public.vehicles
FOR UPDATE
USING (auth.uid() = owner_id)
WITH CHECK (auth.uid() = owner_id);

-- Allow public read access to vehicle data for trip displays
CREATE POLICY "Public can read vehicle data for trips"
ON public.vehicles
FOR SELECT
USING (true);

-- =====================================================
-- ENABLE ROW LEVEL SECURITY
-- =====================================================

-- Enable RLS on storage.objects (if not already enabled)
ALTER TABLE storage.objects ENABLE ROW LEVEL SECURITY;

-- Enable RLS on relevant tables
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.driver_licenses ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.vehicles ENABLE ROW LEVEL SECURITY;

-- =====================================================
-- VERIFICATION QUERIES
-- =====================================================

-- Verify bucket configuration
SELECT id, name, public, created_at 
FROM storage.buckets 
WHERE id IN ('profile-images', 'driver-licenses');

-- Verify storage policies
SELECT schemaname, tablename, policyname, permissive, roles, cmd, qual
FROM pg_policies 
WHERE schemaname = 'storage' AND tablename = 'objects'
ORDER BY policyname;

-- Verify table policies
SELECT schemaname, tablename, policyname, permissive, roles, cmd, qual
FROM pg_policies 
WHERE schemaname = 'public' 
AND tablename IN ('users', 'driver_licenses', 'vehicles')
ORDER BY tablename, policyname;
