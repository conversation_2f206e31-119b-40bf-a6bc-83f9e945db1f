import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:image_picker/image_picker.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/foundation.dart';
import '../../constants/app_theme.dart';
import '../../providers/auth_provider.dart';
import '../../widgets/custom_button.dart';
import '../../widgets/custom_text_field.dart';
import '../../widgets/web_image_preview.dart';
import '../../services/storage_service.dart';
import '../../services/wallet_service.dart';
import '../../models/vehicle_model.dart';
import '../../models/leader_document_model.dart';

class BecomeLeaderPage extends StatefulWidget {
  const BecomeLeaderPage({super.key});

  @override
  State<BecomeLeaderPage> createState() => _BecomeLeaderPageState();
}

class _BecomeLeaderPageState extends State<BecomeLeaderPage> {
  final PageController _pageController = PageController();
  int _currentStep = 0;
  bool _isLoading = false;

  // Step 1: License Upload
  XFile? _licenseImage;
  final _licenseNumberController = TextEditingController();

  // Step 2: Vehicle Info
  final _makeController = TextEditingController();
  final _modelController = TextEditingController();
  final _yearController = TextEditingController();
  final _colorController = TextEditingController();
  final _plateNumberController = TextEditingController();
  final _seatsController = TextEditingController(text: '4');
  XFile? _vehicleImage;

  // Step 3: Payment (Mock)
  bool _paymentCompleted = false;

  @override
  void dispose() {
    _pageController.dispose();
    _licenseNumberController.dispose();
    _makeController.dispose();
    _modelController.dispose();
    _yearController.dispose();
    _colorController.dispose();
    _plateNumberController.dispose();
    _seatsController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('تفعيل وضع القائد'),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body: Column(
        children: [
          // Progress Indicator
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: AppColors.primary,
              borderRadius: const BorderRadius.only(
                bottomLeft: Radius.circular(20),
                bottomRight: Radius.circular(20),
              ),
            ),
            child: Row(
              children: [
                for (int i = 0; i < 4; i++)
                  Expanded(
                    child: Container(
                      margin: const EdgeInsets.symmetric(horizontal: 4),
                      height: 4,
                      decoration: BoxDecoration(
                        color: i <= _currentStep
                            ? Colors.white
                            : Colors.white.withValues(alpha: 0.3),
                        borderRadius: BorderRadius.circular(2),
                      ),
                    ),
                  ),
              ],
            ),
          ),

          // Content
          Expanded(
            child: PageView(
              controller: _pageController,
              physics: const NeverScrollableScrollPhysics(),
              onPageChanged: (index) {
                setState(() {
                  _currentStep = index;
                });
              },
              children: [
                _buildInfoStep(),
                _buildLicenseStep(),
                _buildVehicleStep(),
                _buildPaymentStep(),
              ],
            ),
          ),

          // Navigation Buttons
          Container(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                if (_currentStep > 0)
                  Expanded(
                    child: OutlinedButton(
                      onPressed: _previousStep,
                      style: OutlinedButton.styleFrom(
                        padding: const EdgeInsets.symmetric(vertical: 16),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                        side: BorderSide(color: AppColors.primary),
                      ),
                      child: Text(
                        'السابق',
                        style: TextStyle(color: AppColors.primary),
                      ),
                    ),
                  ),
                if (_currentStep > 0) const SizedBox(width: 16),
                Expanded(
                  child: CustomButton(
                    text: _getNextButtonText(),
                    onPressed: _isLoading
                        ? null
                        : _canProceed()
                            ? _nextStep
                            : null,
                    isLoading: _isLoading,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoStep() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const SizedBox(height: 32),
          Container(
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              color: AppColors.secondary.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(16),
              border: Border.all(
                color: AppColors.secondary.withValues(alpha: 0.3),
              ),
            ),
            child: Column(
              children: [
                Icon(
                  Icons.car_rental,
                  size: 64,
                  color: AppColors.secondary,
                ),
                const SizedBox(height: 16),
                Text(
                  '🚗 تفعيل وضع القائد',
                  style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: AppColors.secondary,
                      ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 16),
                Text(
                  'لتصبح قائدًا، تحتاج إلى:',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                ),
                const SizedBox(height: 12),
                _buildRequirementItem('📄 صورة رخصة القيادة'),
                _buildRequirementItem('🚗 معلومات المركبة'),
                _buildRequirementItem('💳 شحن 20 درهم لضمان الجدية'),
                const SizedBox(height: 16),
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.green.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                    border:
                        Border.all(color: Colors.green.withValues(alpha: 0.3)),
                  ),
                  child: Row(
                    children: [
                      const Icon(Icons.star, color: Colors.green),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          '🏆 العمولة مجانية لأول رحلة!',
                          style: TextStyle(
                            color: Colors.green.shade700,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRequirementItem(String text) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          Icon(Icons.check_circle, color: AppColors.secondary, size: 20),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              text,
              style: const TextStyle(fontSize: 16),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLicenseStep() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'رخصة القيادة',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
          ),
          const SizedBox(height: 8),
          Text(
            'يرجى رفع صورة واضحة لرخصة القيادة الخاصة بك',
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                  color: AppColors.textSecondary,
                ),
          ),
          const SizedBox(height: 24),

          // License Image Upload
          GestureDetector(
            onTap: _pickLicenseImage,
            child: Container(
              width: double.infinity,
              height: 200,
              decoration: BoxDecoration(
                color: AppColors.surface,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: _licenseImage != null
                      ? AppColors.secondary
                      : AppColors.textSecondary.withValues(alpha: 0.3),
                  width: 2,
                  style: BorderStyle.solid,
                ),
              ),
              child: _licenseImage != null
                  ? WebImagePreview(
                      imageFile: _licenseImage!,
                      width: double.infinity,
                      height: 200,
                      fit: BoxFit.cover,
                      borderRadius: BorderRadius.circular(10),
                    )
                  : Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.camera_alt,
                          size: 48,
                          color: AppColors.textSecondary,
                        ),
                        const SizedBox(height: 8),
                        Text(
                          'اضغط لرفع صورة الرخصة',
                          style: TextStyle(
                            color: AppColors.textSecondary,
                            fontSize: 16,
                          ),
                        ),
                      ],
                    ),
            ),
          ),

          const SizedBox(height: 16),

          // License Number (Optional)
          CustomTextField(
            controller: _licenseNumberController,
            label: 'رقم الرخصة (اختياري)',
            prefixIcon: Icons.credit_card,
          ),
        ],
      ),
    );
  }

  Widget _buildVehicleStep() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'معلومات المركبة',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
            ),
            const SizedBox(height: 8),
            Text(
              'أدخل معلومات المركبة التي ستستخدمها في الرحلات',
              style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                    color: AppColors.textSecondary,
                  ),
            ),
            const SizedBox(height: 24),

            // Vehicle Image Upload
            GestureDetector(
              onTap: _pickVehicleImage,
              child: Container(
                width: double.infinity,
                height: 150,
                decoration: BoxDecoration(
                  color: AppColors.surface,
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: _vehicleImage != null
                        ? AppColors.secondary
                        : AppColors.textSecondary.withValues(alpha: 0.3),
                    width: 2,
                  ),
                ),
                child: _vehicleImage != null
                    ? WebImagePreview(
                        imageFile: _vehicleImage!,
                        width: double.infinity,
                        height: 150,
                        fit: BoxFit.cover,
                        borderRadius: BorderRadius.circular(10),
                      )
                    : Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.directions_car,
                            size: 48,
                            color: AppColors.textSecondary,
                          ),
                          const SizedBox(height: 8),
                          Text(
                            'صورة المركبة (اختياري)',
                            style: TextStyle(
                              color: AppColors.textSecondary,
                              fontSize: 16,
                            ),
                          ),
                        ],
                      ),
              ),
            ),

            const SizedBox(height: 16),

            // Vehicle Info Fields
            Row(
              children: [
                Expanded(
                  child: CustomTextField(
                    controller: _makeController,
                    label: 'الماركة',
                    prefixIcon: Icons.branding_watermark,
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'الماركة مطلوبة';
                      }
                      return null;
                    },
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: CustomTextField(
                    controller: _modelController,
                    label: 'الموديل',
                    prefixIcon: Icons.car_rental,
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'الموديل مطلوب';
                      }
                      return null;
                    },
                  ),
                ),
              ],
            ),

            const SizedBox(height: 16),

            Row(
              children: [
                Expanded(
                  child: CustomTextField(
                    controller: _yearController,
                    label: 'السنة',
                    prefixIcon: Icons.calendar_today,
                    keyboardType: TextInputType.number,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: CustomTextField(
                    controller: _colorController,
                    label: 'اللون',
                    prefixIcon: Icons.palette,
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'اللون مطلوب';
                      }
                      return null;
                    },
                  ),
                ),
              ],
            ),

            const SizedBox(height: 16),

            Row(
              children: [
                Expanded(
                  child: CustomTextField(
                    controller: _plateNumberController,
                    label: 'رقم اللوحة',
                    prefixIcon: Icons.confirmation_number,
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'رقم اللوحة مطلوب';
                      }
                      return null;
                    },
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: CustomTextField(
                    controller: _seatsController,
                    label: 'عدد المقاعد',
                    prefixIcon: Icons.event_seat,
                    keyboardType: TextInputType.number,
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'عدد المقاعد مطلوب';
                      }
                      final seats = int.tryParse(value);
                      if (seats == null || seats < 2 || seats > 8) {
                        return 'عدد المقاعد يجب أن يكون بين 2 و 8';
                      }
                      return null;
                    },
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPaymentStep() {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    final currentUser = authProvider.currentUser;
    final hasEnoughBalance = currentUser != null && currentUser.balance >= 20.0;

    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'الدفع والتفعيل',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
          ),
          const SizedBox(height: 8),
          Text(
            'رسوم تفعيل 20 درهم لضمان الجدية وتفعيل حسابك كقائد رحلات',
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                  color: AppColors.textSecondary,
                ),
          ),
          const SizedBox(height: 16),

          // Current Balance Display
          if (currentUser != null) ...[
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: hasEnoughBalance
                    ? Colors.green.withValues(alpha: 0.1)
                    : Colors.orange.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: hasEnoughBalance
                      ? Colors.green.withValues(alpha: 0.3)
                      : Colors.orange.withValues(alpha: 0.3),
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    hasEnoughBalance
                        ? Icons.account_balance_wallet
                        : Icons.warning,
                    color: hasEnoughBalance ? Colors.green : Colors.orange,
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'رصيدك الحالي: ${currentUser.displayBalance}',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            color: hasEnoughBalance
                                ? Colors.green.shade700
                                : Colors.orange.shade700,
                          ),
                        ),
                        if (!hasEnoughBalance) ...[
                          const SizedBox(height: 4),
                          Text(
                            'تحتاج إلى شحن محفظتك لتفعيل وضع القائد',
                            style: TextStyle(
                              color: Colors.orange.shade600,
                              fontSize: 12,
                            ),
                          ),
                          const SizedBox(height: 8),
                          ElevatedButton.icon(
                            onPressed: () => _showChargeWalletDialog(),
                            icon: const Icon(Icons.add, size: 16),
                            label: const Text('شحن المحفظة'),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.orange,
                              foregroundColor: Colors.white,
                              padding: const EdgeInsets.symmetric(
                                  horizontal: 12, vertical: 6),
                              textStyle: const TextStyle(fontSize: 12),
                            ),
                          ),
                        ],
                      ],
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 16),
          ],

          const SizedBox(height: 16),

          // Payment Card
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              gradient: const LinearGradient(
                colors: [AppColors.primary, AppColors.secondary],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: AppColors.primary.withValues(alpha: 0.3),
                  blurRadius: 10,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: Column(
              children: [
                const Icon(
                  Icons.account_balance_wallet,
                  size: 48,
                  color: Colors.white,
                ),
                const SizedBox(height: 16),
                Text(
                  '20.00 درهم',
                  style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                ),
                const SizedBox(height: 8),
                Text(
                  'رسوم التفعيل',
                  style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                        color: Colors.white.withValues(alpha: 0.9),
                      ),
                ),
              ],
            ),
          ),

          const SizedBox(height: 24),

          // Payment Status
          if (!_paymentCompleted)
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.orange.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.orange.withValues(alpha: 0.3)),
              ),
              child: Row(
                children: [
                  const Icon(Icons.info, color: Colors.orange),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      'هذا مجرد محاكاة للدفع. في التطبيق الحقيقي، ستتم عملية الدفع عبر بوابة دفع آمنة.',
                      style: TextStyle(color: Colors.orange.shade700),
                    ),
                  ),
                ],
              ),
            )
          else
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.green.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.green.withValues(alpha: 0.3)),
              ),
              child: Row(
                children: [
                  const Icon(Icons.check_circle, color: Colors.green),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      'تم الدفع بنجاح! ✅',
                      style: TextStyle(
                        color: Colors.green.shade700,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
              ),
            ),
        ],
      ),
    );
  }

  // Helper Methods
  Future<void> _pickLicenseImage() async {
    try {
      if (kIsWeb) {
        // Use file_picker for web
        FilePickerResult? result = await FilePicker.platform.pickFiles(
          type: FileType.image,
          allowMultiple: false,
        );

        if (result != null && result.files.single.bytes != null) {
          // Create XFile from bytes for web
          final file = XFile.fromData(
            result.files.single.bytes!,
            name: result.files.single.name,
            mimeType: 'image/${result.files.single.extension}',
          );
          setState(() {
            _licenseImage = file;
          });
        }
      } else {
        // Use image_picker for mobile
        final ImagePicker picker = ImagePicker();
        final XFile? image = await picker.pickImage(
          source: ImageSource.gallery,
          maxWidth: 1024,
          maxHeight: 1024,
          imageQuality: 85,
        );

        if (image != null) {
          setState(() {
            _licenseImage = image;
          });
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('فشل في اختيار الصورة: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _pickVehicleImage() async {
    try {
      if (kIsWeb) {
        // Use file_picker for web
        FilePickerResult? result = await FilePicker.platform.pickFiles(
          type: FileType.image,
          allowMultiple: false,
        );

        if (result != null && result.files.single.bytes != null) {
          // Create XFile from bytes for web
          final file = XFile.fromData(
            result.files.single.bytes!,
            name: result.files.single.name,
            mimeType: 'image/${result.files.single.extension}',
          );
          setState(() {
            _vehicleImage = file;
          });
        }
      } else {
        // Use image_picker for mobile
        final ImagePicker picker = ImagePicker();
        final XFile? image = await picker.pickImage(
          source: ImageSource.gallery,
          maxWidth: 1024,
          maxHeight: 1024,
          imageQuality: 85,
        );

        if (image != null) {
          setState(() {
            _vehicleImage = image;
          });
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('فشل في اختيار الصورة: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  bool _canProceed() {
    switch (_currentStep) {
      case 0:
      case 1:
      case 2:
        return true;
      case 3:
        final authProvider = Provider.of<AuthProvider>(context, listen: false);
        final currentUser = authProvider.currentUser;
        final hasEnoughBalance =
            currentUser != null && currentUser.balance >= 20.0;
        return _paymentCompleted || hasEnoughBalance;
      default:
        return true;
    }
  }

  String _getNextButtonText() {
    switch (_currentStep) {
      case 0:
        return 'ابدأ التسجيل';
      case 1:
        return 'التالي';
      case 2:
        return 'التالي';
      case 3:
        final authProvider = Provider.of<AuthProvider>(context, listen: false);
        final currentUser = authProvider.currentUser;
        final hasEnoughBalance =
            currentUser != null && currentUser.balance >= 20.0;

        if (_paymentCompleted) {
          return 'إكمال التفعيل';
        } else if (!hasEnoughBalance) {
          return 'رصيد غير كافي';
        } else {
          return 'Pay 20 MAD';
        }
      default:
        return 'التالي';
    }
  }

  void _previousStep() {
    if (_currentStep > 0) {
      _pageController.previousPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  Future<void> _nextStep() async {
    switch (_currentStep) {
      case 0:
        _pageController.nextPage(
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeInOut,
        );
        break;
      case 1:
        if (_validateLicenseStep()) {
          _pageController.nextPage(
            duration: const Duration(milliseconds: 300),
            curve: Curves.easeInOut,
          );
        }
        break;
      case 2:
        if (_validateVehicleStep()) {
          _pageController.nextPage(
            duration: const Duration(milliseconds: 300),
            curve: Curves.easeInOut,
          );
        }
        break;
      case 3:
        if (!_paymentCompleted) {
          await _simulatePayment();
        } else {
          await _completeActivation();
        }
        break;
    }
  }

  bool _validateLicenseStep() {
    if (_licenseImage == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('يرجى رفع صورة رخصة القيادة'),
          backgroundColor: Colors.red,
        ),
      );
      return false;
    }
    return true;
  }

  bool _validateVehicleStep() {
    if (_makeController.text.isEmpty ||
        _modelController.text.isEmpty ||
        _colorController.text.isEmpty ||
        _plateNumberController.text.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('يرجى ملء جميع الحقول المطلوبة'),
          backgroundColor: Colors.red,
        ),
      );
      return false;
    }

    final seats = int.tryParse(_seatsController.text);
    if (seats == null || seats < 2 || seats > 8) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('عدد المقاعد يجب أن يكون بين 2 و 8'),
          backgroundColor: Colors.red,
        ),
      );
      return false;
    }

    return true;
  }

  Future<void> _simulatePayment() async {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    final currentUser = authProvider.currentUser;

    if (currentUser == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('خطأ: لم يتم العثور على بيانات المستخدم'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    if (currentUser.balance < 20.0) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('تحتاج إلى شحن محفظتك لتفعيل وضع القائد'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    // Simulate payment processing
    await Future.delayed(const Duration(seconds: 2));

    setState(() {
      _paymentCompleted = true;
      _isLoading = false;
    });

    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('تم خصم 20 درهم من رصيدك بنجاح! ✅'),
          backgroundColor: Colors.green,
        ),
      );
    }
  }

  Future<void> _completeActivation() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final authProvider = Provider.of<AuthProvider>(context, listen: false);
      final currentUser = authProvider.currentUser;

      if (currentUser != null) {
        String licenseImageUrl = '';
        String vehicleImageUrl = '';

        // Upload license image if provided
        if (_licenseImage != null) {
          final uploadedLicenseUrl = await StorageService.uploadDriverLicense(
            imageFile: _licenseImage!,
            userId: currentUser.id,
          );
          if (uploadedLicenseUrl != null) {
            licenseImageUrl = uploadedLicenseUrl;
          }
        }

        // Upload vehicle image if provided
        if (_vehicleImage != null) {
          final uploadedVehicleUrl = await StorageService.uploadCarImage(
            imageFile: _vehicleImage!,
            userId: currentUser.id,
          );
          if (uploadedVehicleUrl != null) {
            vehicleImageUrl = uploadedVehicleUrl;
          }
        }

        // Log the results (for debugging)
        if (mounted) {
          debugPrint('Uploaded license image: $licenseImageUrl');
          debugPrint('Uploaded vehicle image: $vehicleImageUrl');
          debugPrint(
              'Vehicle info: ${_makeController.text} ${_modelController.text}');
          debugPrint('Activated trip leader with 20.0 MAD balance');
        }

        // Calculate new balance after deducting activation fee
        final newBalance = currentUser.balance - 20.0;

        // Update auth provider
        await authProvider.updateProfile(currentUser.copyWith(
          isLeader: true,
          balance: newBalance,
          role: 'trip_leader',
        ));

        // Show success message and animation
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('تم تفعيل حساب القائد بنجاح! يمكنك الآن إنشاء الرحلات.'),
              backgroundColor: Colors.green,
              duration: Duration(seconds: 3),
            ),
          );
          await _showSuccessAnimation();
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('حدث خطأ: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _showSuccessAnimation() async {
    await showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 80,
              height: 80,
              decoration: const BoxDecoration(
                color: Colors.green,
                shape: BoxShape.circle,
              ),
              child: const Icon(
                Icons.check,
                color: Colors.white,
                size: 40,
              ),
            ),
            const SizedBox(height: 16),
            Text(
              'أنت الآن قائد معتمد!',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: Colors.green,
                  ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              '🚗 يمكنك الآن إنشاء رحلات وإدارتها',
              style: Theme.of(context).textTheme.bodyLarge,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            SizedBox(
              width: double.infinity,
              child: CustomButton(
                text: 'ابدأ الآن',
                onPressed: () {
                  Navigator.of(context).pop(); // Close dialog
                  Navigator.of(context).pop(); // Go back to profile
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showChargeWalletDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('شحن المحفظة'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text('اختر المبلغ الذي تريد شحنه:'),
            const SizedBox(height: 16),
            Wrap(
              spacing: 8,
              children: [20, 50, 100, 200].map((amount) {
                return ElevatedButton(
                  onPressed: () => _chargeWallet(amount.toDouble()),
                  child: Text('$amount درهم'),
                );
              }).toList(),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
        ],
      ),
    );
  }

  Future<void> _chargeWallet(double amount) async {
    Navigator.pop(context); // Close dialog

    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    final currentUser = authProvider.currentUser;

    if (currentUser != null) {
      // Use WalletService to charge wallet
      final success = await WalletService.chargeWallet(
        userId: currentUser.id,
        amount: amount,
        paymentMethod: 'mock_payment',
      );

      if (success) {
        // Reload user profile to get updated balance
        final updatedUser =
            currentUser.copyWith(balance: currentUser.balance + amount);
        await authProvider.updateProfile(updatedUser);

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content:
                  Text('تم شحن ${WalletService.formatAmount(amount)} بنجاح!'),
              backgroundColor: Colors.green,
            ),
          );
          setState(() {}); // Refresh the UI
        }
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('فشل في شحن المحفظة'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }
}
