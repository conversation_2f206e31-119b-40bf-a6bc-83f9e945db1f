import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'constants/app_theme.dart';
import 'pages/profile/become_leader_page.dart';

void main() {
  runApp(const PersonalInfoDemoApp());
}

class PersonalInfoDemoApp extends StatelessWidget {
  const PersonalInfoDemoApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'سفرني - المعلومات الشخصية',
      debugShowCheckedModeBanner: false,

      // الدعم اللغوي
      localizationsDelegates: const [
        GlobalMaterialLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
      ],
      supportedLocales: const [Locale('ar')],
      locale: const Locale('ar'),

      // الثيم
      theme: AppTheme.lightTheme,

      // الصفحة الرئيسية
      home: const BecomeLeaderPage(),

      // دعم RTL للعربية
      builder: (context, child) {
        return Directionality(
          textDirection: TextDirection.rtl,
          child: child!,
        );
      },
    );
  }
}
