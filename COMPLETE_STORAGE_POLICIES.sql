-- Complete Storage Policies for Travel App
-- Execute this script in Supabase SQL Editor

-- =====================================================
-- STEP 1: CLEAN UP EXISTING POLICIES
-- =====================================================

-- Drop all existing storage policies to start fresh
DROP POLICY IF EXISTS "Users can upload their own license documents" ON storage.objects;
DROP POLICY IF EXISTS "Users can read their own license documents" ON storage.objects;
DROP POLICY IF EXISTS "Users can update their own license documents" ON storage.objects;
DROP POLICY IF EXISTS "Users can delete their own license documents" ON storage.objects;
DROP POLICY IF EXISTS "Admins can read all license documents" ON storage.objects;
DROP POLICY IF EXISTS "driver_license_upload_policy" ON storage.objects;
DROP POLICY IF EXISTS "driver_license_read_policy" ON storage.objects;
DROP POLICY IF EXISTS "driver_license_update_policy" ON storage.objects;
DROP POLICY IF EXISTS "driver_license_delete_policy" ON storage.objects;
DROP POLICY IF EXISTS "admin_license_read_policy" ON storage.objects;
DROP POLICY IF EXISTS "Public read access for profile images" ON storage.objects;
DROP POLICY IF EXISTS "Users can upload their own profile images" ON storage.objects;
DROP POLICY IF EXISTS "Users can update their own profile images" ON storage.objects;
DROP POLICY IF EXISTS "Users can delete their own profile images" ON storage.objects;
DROP POLICY IF EXISTS "Drivers can upload their licenses" ON storage.objects;
DROP POLICY IF EXISTS "Drivers can read their own license" ON storage.objects;

-- =====================================================
-- STEP 2: CREATE/UPDATE BUCKETS
-- =====================================================

-- Create driver-licenses bucket (private)
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES (
  'driver-licenses', 
  'driver-licenses', 
  false, -- Private bucket
  5242880, -- 5MB limit
  ARRAY['image/jpeg', 'image/jpg', 'image/png', 'image/webp']
)
ON CONFLICT (id) DO UPDATE SET
  name = 'driver-licenses',
  public = false,
  file_size_limit = 5242880,
  allowed_mime_types = ARRAY['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];

-- Create profile-images bucket (public)
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES (
  'profile-images', 
  'profile-images', 
  true, -- Public bucket
  2097152, -- 2MB limit for profile images
  ARRAY['image/jpeg', 'image/jpg', 'image/png', 'image/webp']
)
ON CONFLICT (id) DO UPDATE SET
  name = 'profile-images',
  public = true,
  file_size_limit = 2097152,
  allowed_mime_types = ARRAY['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];

-- =====================================================
-- STEP 3: DRIVER-LICENSES BUCKET POLICIES (PRIVATE)
-- =====================================================

-- Policy 1: Drivers can upload their licenses with metadata check
CREATE POLICY "Drivers can upload their licenses"
ON storage.objects
FOR INSERT
WITH CHECK (
  bucket_id = 'driver-licenses' 
  AND auth.role() = 'authenticated'
  AND name LIKE 'licenses/license_' || auth.uid()::text || '.%'
  AND metadata->>'owner' = auth.uid()::text
);

-- Policy 2: Drivers can read their own license
CREATE POLICY "Drivers can read their own license"
ON storage.objects
FOR SELECT
USING (
  bucket_id = 'driver-licenses' 
  AND auth.role() = 'authenticated'
  AND (
    name LIKE 'licenses/license_' || auth.uid()::text || '.%'
    OR metadata->>'owner' = auth.uid()::text
  )
);

-- Policy 3: Drivers can update their own license
CREATE POLICY "Drivers can update their own license"
ON storage.objects
FOR UPDATE
USING (
  bucket_id = 'driver-licenses' 
  AND auth.role() = 'authenticated'
  AND (
    name LIKE 'licenses/license_' || auth.uid()::text || '.%'
    OR metadata->>'owner' = auth.uid()::text
  )
);

-- Policy 4: Drivers can delete their own license
CREATE POLICY "Drivers can delete their own license"
ON storage.objects
FOR DELETE
USING (
  bucket_id = 'driver-licenses' 
  AND auth.role() = 'authenticated'
  AND (
    name LIKE 'licenses/license_' || auth.uid()::text || '.%'
    OR metadata->>'owner' = auth.uid()::text
  )
);

-- Policy 5: Admins can read all licenses for verification
CREATE POLICY "Admins can read all licenses"
ON storage.objects
FOR SELECT
USING (
  bucket_id = 'driver-licenses' 
  AND auth.role() = 'authenticated'
  AND name LIKE 'licenses/%'
  AND EXISTS (
    SELECT 1 FROM public.users 
    WHERE id = auth.uid() 
    AND (role = 'admin' OR role = 'super_admin')
  )
);

-- =====================================================
-- STEP 4: PROFILE-IMAGES BUCKET POLICIES (PUBLIC)
-- =====================================================

-- Policy 1: Public read access for all profile images
CREATE POLICY "Public read access for profile images"
ON storage.objects
FOR SELECT
USING (bucket_id = 'profile-images');

-- Policy 2: Users can upload their own profile images
CREATE POLICY "Users can upload their own profile images"
ON storage.objects
FOR INSERT
WITH CHECK (
  bucket_id = 'profile-images' 
  AND auth.role() = 'authenticated'
  AND (
    name LIKE 'users/profile_' || auth.uid()::text || '.%'
    OR name LIKE 'vehicles/vehicle_' || auth.uid()::text || '.%'
  )
);

-- Policy 3: Users can update their own profile images
CREATE POLICY "Users can update their own profile images"
ON storage.objects
FOR UPDATE
USING (
  bucket_id = 'profile-images' 
  AND auth.role() = 'authenticated'
  AND (
    name LIKE 'users/profile_' || auth.uid()::text || '.%'
    OR name LIKE 'vehicles/vehicle_' || auth.uid()::text || '.%'
  )
);

-- Policy 4: Users can delete their own profile images
CREATE POLICY "Users can delete their own profile images"
ON storage.objects
FOR DELETE
USING (
  bucket_id = 'profile-images' 
  AND auth.role() = 'authenticated'
  AND (
    name LIKE 'users/profile_' || auth.uid()::text || '.%'
    OR name LIKE 'vehicles/vehicle_' || auth.uid()::text || '.%'
  )
);

-- =====================================================
-- STEP 5: ENABLE ROW LEVEL SECURITY
-- =====================================================

-- Ensure RLS is enabled on storage.objects
ALTER TABLE storage.objects ENABLE ROW LEVEL SECURITY;

-- Enable RLS on user tables
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;

-- =====================================================
-- STEP 6: USER TABLE POLICIES
-- =====================================================

-- Allow users to read their own profile
CREATE POLICY "Users can read their own profile" ON public.users
FOR SELECT USING (auth.uid() = id);

-- Allow users to update their own profile
CREATE POLICY "Users can update their own profile" ON public.users
FOR UPDATE USING (auth.uid() = id) WITH CHECK (auth.uid() = id);

-- Allow public read access to user profiles for trip displays
CREATE POLICY "Public can read user profiles for trips" ON public.users
FOR SELECT USING (true);

-- =====================================================
-- STEP 7: VERIFICATION QUERIES
-- =====================================================

-- Verify bucket configuration
SELECT 
  id, 
  name, 
  public, 
  file_size_limit,
  allowed_mime_types,
  created_at 
FROM storage.buckets 
WHERE id IN ('driver-licenses', 'profile-images')
ORDER BY id;

-- Verify storage policies
SELECT 
  schemaname, 
  tablename, 
  policyname, 
  permissive, 
  roles, 
  cmd, 
  qual
FROM pg_policies 
WHERE schemaname = 'storage' 
  AND tablename = 'objects'
  AND (policyname LIKE '%license%' OR policyname LIKE '%profile%')
ORDER BY policyname;

-- Check RLS status
SELECT schemaname, tablename, rowsecurity 
FROM pg_tables 
WHERE schemaname IN ('storage', 'public') 
  AND tablename IN ('objects', 'users');

-- =====================================================
-- NOTES FOR DEVELOPERS
-- =====================================================

/*
BUCKET STRUCTURE:

driver-licenses/ (private)
└── licenses/
    └── license_<user_id>.<ext>
    
profile-images/ (public)
├── users/
│   └── profile_<user_id>.<ext>
└── vehicles/
    └── vehicle_<user_id>.<ext>

METADATA REQUIREMENTS:
- driver-licenses: Must include "owner": user_id
- profile-images: No metadata required (public)

URL GENERATION:
- Profile images: getPublicUrl()
- License images: createSignedUrl() with 60s expiry

SECURITY:
- Users can only access their own files
- Profile images are publicly readable
- License images require authentication
- Admins can read all licenses for verification
*/
