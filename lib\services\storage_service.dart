import 'dart:typed_data';
import 'package:flutter/foundation.dart';
import 'package:image_picker/image_picker.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:uuid/uuid.dart';

class StorageService {
  static final SupabaseClient _supabase = Supabase.instance.client;
  static const String _driverLicensesBucket = 'driver-licenses'; // Private bucket
  static const String _profileImagesBucket = 'profile-images';   // Public bucket
  static const String _tripGalleryBucket = 'trip_gallery';

  /// Upload an image to Supabase Storage with proper access control
  /// This method works for both web and mobile platforms
  static Future<String?> uploadImage({
    required XFile imageFile,
    required String bucket,
    String? folder,
    String? customFileName,
    String? userId,
    bool isPrivate = false,
  }) async {
    try {
      // Generate unique filename
      final uuid = const Uuid();
      final fileExtension = imageFile.name.split('.').last.toLowerCase();
      final fileName = customFileName ?? '${uuid.v4()}.$fileExtension';
      final fullPath = folder != null ? '$folder/$fileName' : fileName;

      // Read image bytes (works for both web and mobile)
      final Uint8List imageBytes = await imageFile.readAsBytes();

      // Prepare upload options
      const uploadOptions = FileOptions(
        cacheControl: '3600',
        upsert: true,
      );

      // Upload to Supabase Storage
      final response = await _supabase.storage
          .from(bucket)
          .uploadBinary(fullPath, imageBytes, fileOptions: uploadOptions);

      if (response.isNotEmpty) {
        // Return the file path for private files, public URL for public files
        if (isPrivate) {
          return fullPath; // Return path for signed URL generation
        } else {
          // Get public URL for public files
          final publicUrl = _supabase.storage
              .from(bucket)
              .getPublicUrl(fullPath);

          return publicUrl;
        }
      }

      return null;
    } catch (e) {
      if (kDebugMode) {
        print('Error uploading image: $e');
      }
      return null;
    }
  }

  /// Upload profile image
  static Future<String?> uploadProfileImage({
    required XFile imageFile,
    required String userId,
  }) async {
    return await uploadImage(
      imageFile: imageFile,
      bucket: _profileImagesBucket,
      folder: 'users',
      customFileName: 'profile_$userId.${imageFile.name.split('.').last}',
    );
  }

  /// Upload driver license image to private bucket
  static Future<String?> uploadDriverLicense({
    required XFile imageFile,
    required String userId,
  }) async {
    try {
      // Get current user to ensure we have authentication
      final currentUser = _supabase.auth.currentUser;
      if (currentUser == null) {
        throw Exception('User not authenticated');
      }

      // Ensure the user ID matches the authenticated user for security
      if (currentUser.id != userId) {
        throw Exception('User ID mismatch - security violation');
      }

      // Use the correct path structure: licenses/license_<userID>.extension
      final fileExtension = imageFile.name.split('.').last.toLowerCase();
      final fileName = 'license_$userId.$fileExtension';
      final fullPath = 'licenses/$fileName'; // This matches the RLS policy path

      // Read image bytes
      final Uint8List imageBytes = await imageFile.readAsBytes();

      // Upload with proper options
      const uploadOptions = FileOptions(
        cacheControl: '3600',
        upsert: true,
      );

      // Upload to private driver-licenses bucket
      final response = await _supabase.storage
          .from(_driverLicensesBucket)
          .uploadBinary(fullPath, imageBytes, fileOptions: uploadOptions);

      if (response.isNotEmpty) {
        if (kDebugMode) {
          print('License uploaded successfully to: $fullPath');
        }
        return fullPath; // Return path for signed URL generation
      }

      return null;
    } catch (e) {
      if (kDebugMode) {
        print('Error uploading driver license: $e');
      }
      return null;
    }
  }

  /// Upload car image to public bucket
  static Future<String?> uploadCarImage({
    required XFile imageFile,
    required String userId,
  }) async {
    return await uploadImage(
      imageFile: imageFile,
      bucket: _profileImagesBucket,
      folder: 'vehicles',
      customFileName: 'vehicle_$userId.${imageFile.name.split('.').last}',
      userId: userId,
      isPrivate: false,
    );
  }

  /// Upload trip gallery image
  static Future<String?> uploadTripImage({
    required XFile imageFile,
    required String tripId,
    int? imageIndex,
  }) async {
    final fileName = imageIndex != null 
        ? 'trip_${tripId}_$imageIndex.${imageFile.name.split('.').last}'
        : null;
    
    return await uploadImage(
      imageFile: imageFile,
      bucket: _tripGalleryBucket,
      folder: 'trips',
      customFileName: fileName,
    );
  }

  /// Delete an image from storage
  static Future<bool> deleteImage({
    required String bucket,
    required String filePath,
  }) async {
    try {
      await _supabase.storage
          .from(bucket)
          .remove([filePath]);
      return true;
    } catch (e) {
      if (kDebugMode) {
        print('Error deleting image: $e');
      }
      return false;
    }
  }

  /// Get file path from public URL
  static String? getFilePathFromUrl(String publicUrl, String bucket) {
    try {
      final uri = Uri.parse(publicUrl);
      final pathSegments = uri.pathSegments;
      final bucketIndex = pathSegments.indexOf(bucket);
      
      if (bucketIndex != -1 && bucketIndex < pathSegments.length - 1) {
        return pathSegments.sublist(bucketIndex + 1).join('/');
      }
      
      return null;
    } catch (e) {
      if (kDebugMode) {
        print('Error parsing URL: $e');
      }
      return null;
    }
  }

  /// Get profile image URL for a user with fallback logic
  static String? getProfileImageUrl(String userId, {String? storedUrl}) {
    try {
      // If we have a stored URL, use it
      if (storedUrl != null && storedUrl.isNotEmpty) {
        return storedUrl;
      }

      // Try different file extensions in profile-images/users/
      final extensions = ['jpg', 'jpeg', 'png', 'webp'];
      for (final ext in extensions) {
        final filePath = 'users/profile_$userId.$ext';
        final url = _supabase.storage
            .from(_profileImagesBucket)
            .getPublicUrl(filePath);

        if (url.isNotEmpty) {
          return url;
        }
      }

      return null;
    } catch (e) {
      if (kDebugMode) {
        print('Error getting profile image URL: $e');
      }
      return null;
    }
  }

  /// Get driver license signed URL for secure access (60 seconds validity)
  static Future<String?> getDriverLicenseSignedUrl(String userId) async {
    try {
      // Get current user to ensure authentication
      final currentUser = _supabase.auth.currentUser;
      if (currentUser == null) {
        if (kDebugMode) {
          print('User not authenticated for license access');
        }
        return null;
      }

      // Ensure the user can only access their own license
      if (currentUser.id != userId) {
        if (kDebugMode) {
          print('User ID mismatch - cannot access license');
        }
        return null;
      }

      // Try different file extensions with correct path structure
      final extensions = ['jpg', 'jpeg', 'png', 'webp'];
      for (final ext in extensions) {
        final filePath = 'licenses/license_$userId.$ext'; // Correct path structure

        try {
          // Create signed URL with 60 seconds expiry
          final signedUrl = await _supabase.storage
              .from(_driverLicensesBucket)
              .createSignedUrl(filePath, 60);

          if (signedUrl.isNotEmpty) {
            if (kDebugMode) {
              print('Generated signed URL for: $filePath');
            }
            return signedUrl;
          }
        } catch (e) {
          // Continue to next extension if this one fails
          if (kDebugMode) {
            print('Failed to generate signed URL for $filePath: $e');
          }
          continue;
        }
      }

      return null;
    } catch (e) {
      if (kDebugMode) {
        print('Error getting license signed URL: $e');
      }
      return null;
    }
  }

  /// Check if driver license exists for a user
  static Future<bool> driverLicenseExists(String userId) async {
    try {
      // Get current user to ensure authentication
      final currentUser = _supabase.auth.currentUser;
      if (currentUser == null || currentUser.id != userId) {
        return false;
      }

      final extensions = ['jpg', 'jpeg', 'png', 'webp'];
      for (final ext in extensions) {
        final filePath = 'licenses/license_$userId.$ext'; // Correct path structure

        try {
          // Try to create a signed URL - if successful, file exists
          await _supabase.storage
              .from(_driverLicensesBucket)
              .createSignedUrl(filePath, 1);
          return true;
        } catch (e) {
          // Continue to next extension
          continue;
        }
      }
      return false;
    } catch (e) {
      if (kDebugMode) {
        print('Error checking license existence: $e');
      }
      return false;
    }
  }

  /// Get car image URL for a user (using public profile-images bucket)
  static String? getCarImageUrl(String userId) {
    try {
      // Try different file extensions in profile-images/vehicles/
      final extensions = ['jpg', 'jpeg', 'png', 'webp'];
      for (final ext in extensions) {
        final filePath = 'vehicles/vehicle_$userId.$ext';
        final url = _supabase.storage
            .from(_profileImagesBucket)
            .getPublicUrl(filePath);

        if (url.isNotEmpty) {
          return url;
        }
      }

      return null;
    } catch (e) {
      if (kDebugMode) {
        print('Error getting car image URL: $e');
      }
      return null;
    }
  }

  /// Check if an image exists in storage
  static Future<bool> imageExists({
    required String bucket,
    required String filePath,
  }) async {
    try {
      final pathParts = filePath.split('/');
      final folder = pathParts.length > 1 ? pathParts.first : '';
      final fileName = pathParts.last;

      final response = await _supabase.storage
          .from(bucket)
          .list(path: folder);

      return response.any((file) => file.name == fileName);
    } catch (e) {
      if (kDebugMode) {
        print('Error checking image existence: $e');
      }
      return false;
    }
  }

  /// Check if storage buckets exist and are accessible
  static Future<bool> checkStorageHealth() async {
    try {
      // Try to list files in each bucket to verify access
      await _supabase.storage.from(_driverLicensesBucket).list();
      await _supabase.storage.from(_profileImagesBucket).list();
      await _supabase.storage.from(_tripGalleryBucket).list();
      return true;
    } catch (e) {
      if (kDebugMode) {
        print('Storage health check failed: $e');
      }
      return false;
    }
  }
}
