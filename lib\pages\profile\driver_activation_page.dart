import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart' as provider;
import 'package:image_picker/image_picker.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/foundation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../../constants/app_theme.dart';
import '../../providers/auth_provider.dart';
import '../../widgets/custom_button.dart';
import '../../widgets/custom_text_field.dart';
import '../../widgets/web_image_preview.dart';
import '../../widgets/safe_network_image.dart';
import '../../services/storage_service.dart';
import '../../models/user_model.dart';

class DriverActivationPage extends StatefulWidget {
  const DriverActivationPage({super.key});

  @override
  State<DriverActivationPage> createState() => _DriverActivationPageState();
}

class _DriverActivationPageState extends State<DriverActivationPage>
    with TickerProviderStateMixin {
  final PageController _pageController = PageController();
  int _currentStep = 0;
  bool _isLoading = false;

  // Animation controllers
  late AnimationController _fadeController;
  late AnimationController _slideController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  // Step 1: Personal Information
  final _nameController = TextEditingController();
  XFile? _profileImage;

  // Step 2: Driver's License
  final _licenseNumberController = TextEditingController();
  XFile? _licenseImage;

  // Step 3: Vehicle Information
  final _vehicleMakeController = TextEditingController();
  final _vehicleModelController = TextEditingController();
  final _vehicleColorController = TextEditingController();
  final _vehiclePlateController = TextEditingController();
  final _vehicleSeatsController = TextEditingController(text: '4');
  XFile? _vehicleImage;

  // Step 4: Payment
  bool _paymentCompleted = false;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _loadCurrentUserData();
  }

  void _initializeAnimations() {
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    
    _slideController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeInOut,
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _slideController,
      curve: Curves.easeOutCubic,
    ));

    _fadeController.forward();
    _slideController.forward();
  }

  void _loadCurrentUserData() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final authProvider = provider.Provider.of<AuthProvider>(context, listen: false);
      final currentUser = authProvider.currentUser;

      if (currentUser != null) {
        _nameController.text = currentUser.fullName;
      }
    });
  }

  @override
  void dispose() {
    _pageController.dispose();
    _fadeController.dispose();
    _slideController.dispose();
    _nameController.dispose();
    _licenseNumberController.dispose();
    _vehicleMakeController.dispose();
    _vehicleModelController.dispose();
    _vehicleColorController.dispose();
    _vehiclePlateController.dispose();
    _vehicleSeatsController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        elevation: 0,
        title: Text(
          'تفعيل وضع السائق',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        centerTitle: true,
      ),
      body: Column(
        children: [
          _buildProgressIndicator(),
          Expanded(
            child: FadeTransition(
              opacity: _fadeAnimation,
              child: SlideTransition(
                position: _slideAnimation,
                child: PageView(
                  controller: _pageController,
                  physics: const NeverScrollableScrollPhysics(),
                  onPageChanged: (index) {
                    setState(() {
                      _currentStep = index;
                    });
                  },
                  children: [
                    _buildPersonalInfoStep(),
                    _buildLicenseStep(),
                    _buildVehicleStep(),
                    _buildPaymentStep(),
                  ],
                ),
              ),
            ),
          ),
          _buildNavigationButtons(),
        ],
      ),
    );
  }

  Widget _buildProgressIndicator() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: AppColors.primary,
        borderRadius: const BorderRadius.only(
          bottomLeft: Radius.circular(24),
          bottomRight: Radius.circular(24),
        ),
      ),
      child: Column(
        children: [
          Row(
            children: [
              Text(
                'الخطوة ${_currentStep + 1} من 4',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
              const Spacer(),
              Text(
                '${((_currentStep + 1) * 25).toInt()}%',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          LinearProgressIndicator(
            value: (_currentStep + 1) / 4,
            backgroundColor: Colors.white.withValues(alpha: 0.3),
            valueColor: const AlwaysStoppedAnimation<Color>(Colors.white),
            borderRadius: BorderRadius.circular(8),
            minHeight: 8,
          ),
        ],
      ),
    );
  }

  Widget _buildPersonalInfoStep() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          _buildStepHeader(
            '👤 المعلومات الشخصية',
            'أدخل اسمك الكامل وارفع صورة شخصية واضحة. ستظهر هذه المعلومات للركاب.',
            AppColors.secondary,
          ),
          const SizedBox(height: 40),
          _buildAvatarSection(),
          const SizedBox(height: 40),
          _buildNameField(),
          const SizedBox(height: 20),
        ],
      ),
    );
  }

  Widget _buildLicenseStep() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          _buildStepHeader(
            '📄 رخصة القيادة',
            'ارفع صورة واضحة لرخصة القيادة الخاصة بك وأدخل المعلومات المطلوبة.',
            AppColors.accent,
          ),
          const SizedBox(height: 40),
          _buildLicenseImageSection(),
          const SizedBox(height: 30),
          _buildLicenseFields(),
          const SizedBox(height: 20),
        ],
      ),
    );
  }

  Widget _buildVehicleStep() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          _buildStepHeader(
            '🚗 معلومات المركبة',
            'أدخل تفاصيل مركبتك وارفع صورة واضحة لها.',
            AppColors.primary,
          ),
          const SizedBox(height: 40),
          _buildVehicleImageSection(),
          const SizedBox(height: 30),
          _buildVehicleFields(),
          const SizedBox(height: 20),
        ],
      ),
    );
  }

  Widget _buildPaymentStep() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          _buildStepHeader(
            '💳 شحن الحساب',
            'اشحن حسابك بـ 20 درهم لضمان الجدية والبدء في تقديم الخدمة.',
            AppColors.success,
          ),
          const SizedBox(height: 40),
          _buildPaymentSection(),
          const SizedBox(height: 20),
        ],
      ),
    );
  }

  Widget _buildStepHeader(String title, String description, Color color) {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            color.withValues(alpha: 0.1),
            color.withValues(alpha: 0.05),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: color.withValues(alpha: 0.2),
        ),
      ),
      child: Column(
        children: [
          Text(
            title,
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const SizedBox(height: 12),
          Text(
            description,
            textAlign: TextAlign.center,
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
              color: AppColors.textSecondary,
              height: 1.6,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAvatarSection() {
    return Center(
      child: Column(
        children: [
          Stack(
            children: [
              Container(
                width: 140,
                height: 140,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      AppColors.secondary.withValues(alpha: 0.1),
                      AppColors.primary.withValues(alpha: 0.1),
                    ],
                  ),
                  border: Border.all(
                    color: AppColors.secondary.withValues(alpha: 0.3),
                    width: 3,
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: AppColors.secondary.withValues(alpha: 0.2),
                      blurRadius: 20,
                      offset: const Offset(0, 8),
                    ),
                  ],
                ),
                child: _profileImage != null
                    ? ClipOval(
                        child: WebImagePreview(
                          imageFile: _profileImage,
                          width: 140,
                          height: 140,
                          fit: BoxFit.cover,
                        ),
                      )
                    : provider.Consumer<AuthProvider>(
                        builder: (context, authProvider, child) {
                          final currentUser = authProvider.currentUser;
                          return ProfileAvatar(
                            imageUrl: currentUser != null
                                ? StorageService.getProfileImageUrl(
                                    currentUser.id,
                                    storedUrl: currentUser.profileImageUrl,
                                  )
                                : null,
                            radius: 70,
                          );
                        },
                      ),
              ),
              Positioned(
                bottom: 0,
                right: 0,
                child: GestureDetector(
                  onTap: _showImageSourceDialog,
                  child: Container(
                    width: 44,
                    height: 44,
                    decoration: BoxDecoration(
                      color: AppColors.secondary,
                      shape: BoxShape.circle,
                      boxShadow: [
                        BoxShadow(
                          color: AppColors.secondary.withValues(alpha: 0.3),
                          blurRadius: 8,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    child: Icon(
                      _profileImage != null ? Icons.edit_rounded : Icons.add_a_photo_rounded,
                      color: Colors.white,
                      size: 20,
                    ),
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Text(
            _profileImage != null ? 'اضغط لتغيير الصورة' : 'اضغط لإضافة صورة شخصية',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: AppColors.textSecondary,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNameField() {
    return CustomTextField(
      controller: _nameController,
      label: 'الاسم الكامل *',
      hint: 'أدخل اسمك الكامل',
      prefixIcon: Icons.person_outline_rounded,
      validator: (value) {
        if (value == null || value.trim().isEmpty) {
          return 'يرجى إدخال اسمك الكامل';
        }
        if (value.trim().length < 4) {
          return 'الاسم يجب أن يكون 4 أحرف على الأقل';
        }
        return null;
      },
    );
  }

  // Image picker methods
  void _showImageSourceDialog() {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => _buildImageSourceBottomSheet(),
    );
  }

  Widget _buildImageSourceBottomSheet() {
    return Container(
      margin: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(20),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            width: 40,
            height: 4,
            margin: const EdgeInsets.only(top: 12),
            decoration: BoxDecoration(
              color: AppColors.textTertiary,
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          Padding(
            padding: const EdgeInsets.all(24),
            child: Column(
              children: [
                Text(
                  'اختر صورة',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 24),
                Row(
                  children: [
                    if (!kIsWeb) ...[
                      Expanded(
                        child: _buildImageSourceOption(
                          icon: Icons.camera_alt_rounded,
                          label: 'الكاميرا',
                          onTap: () {
                            Navigator.pop(context);
                            _pickImage(ImageSource.camera);
                          },
                        ),
                      ),
                      const SizedBox(width: 16),
                    ],
                    Expanded(
                      child: _buildImageSourceOption(
                        icon: Icons.photo_library_rounded,
                        label: kIsWeb ? 'اختر صورة' : 'المعرض',
                        onTap: () {
                          Navigator.pop(context);
                          _pickImage(ImageSource.gallery);
                        },
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildImageSourceOption({
    required IconData icon,
    required String label,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(16),
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 20, horizontal: 16),
        decoration: BoxDecoration(
          color: AppColors.surfaceVariant.withValues(alpha: 0.5),
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: AppColors.border.withValues(alpha: 0.3),
          ),
        ),
        child: Column(
          children: [
            Icon(
              icon,
              size: 32,
              color: AppColors.primary,
            ),
            const SizedBox(height: 8),
            Text(
              label,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _pickImage(ImageSource source) async {
    try {
      XFile? pickedFile;

      if (kIsWeb) {
        FilePickerResult? result = await FilePicker.platform.pickFiles(
          type: FileType.image,
          allowMultiple: false,
        );

        if (result != null && result.files.single.bytes != null) {
          pickedFile = XFile.fromData(
            result.files.single.bytes!,
            name: result.files.single.name,
          );
        }
      } else {
        pickedFile = await ImagePicker().pickImage(
          source: source,
          maxWidth: 512,
          maxHeight: 512,
          imageQuality: 85,
        );
      }

      if (pickedFile != null) {
        setState(() {
          if (_currentStep == 0) {
            _profileImage = pickedFile;
          } else if (_currentStep == 1) {
            _licenseImage = pickedFile;
          } else if (_currentStep == 2) {
            _vehicleImage = pickedFile;
          }
        });

        HapticFeedback.lightImpact();
      }
    } catch (e) {
      _showErrorSnackBar('فشل في اختيار الصورة');
    }
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppColors.error,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        margin: const EdgeInsets.all(16),
      ),
    );
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppColors.success,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        margin: const EdgeInsets.all(16),
      ),
    );
  }

  // License Step Components
  Widget _buildLicenseImageSection() {
    return Center(
      child: GestureDetector(
        onTap: _showImageSourceDialog,
        child: AnimatedContainer(
          duration: const Duration(milliseconds: 300),
          width: double.infinity,
          height: 220,
          decoration: BoxDecoration(
            gradient: _licenseImage != null
                ? null
                : LinearGradient(
                    colors: [
                      AppColors.accent.withValues(alpha: 0.1),
                      AppColors.accent.withValues(alpha: 0.05),
                    ],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
            borderRadius: BorderRadius.circular(20),
            border: Border.all(
              color: _licenseImage != null
                  ? AppColors.accent.withValues(alpha: 0.5)
                  : AppColors.accent.withValues(alpha: 0.3),
              width: 2,
            ),
            boxShadow: [
              BoxShadow(
                color: AppColors.accent.withValues(alpha: 0.1),
                blurRadius: 12,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: _licenseImage != null
              ? Stack(
                  children: [
                    ClipRRect(
                      borderRadius: BorderRadius.circular(18),
                      child: WebImagePreview(
                        imageFile: _licenseImage,
                        width: double.infinity,
                        height: 220,
                        fit: BoxFit.cover,
                      ),
                    ),
                    Positioned(
                      top: 12,
                      right: 12,
                      child: Container(
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: AppColors.success,
                          shape: BoxShape.circle,
                          boxShadow: [
                            BoxShadow(
                              color: AppColors.success.withValues(alpha: 0.3),
                              blurRadius: 8,
                              offset: const Offset(0, 2),
                            ),
                          ],
                        ),
                        child: const Icon(
                          Icons.check_rounded,
                          color: Colors.white,
                          size: 16,
                        ),
                      ),
                    ),
                    Positioned(
                      bottom: 12,
                      left: 12,
                      right: 12,
                      child: Container(
                        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                        decoration: BoxDecoration(
                          color: Colors.black.withValues(alpha: 0.7),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Text(
                          'اضغط لتغيير الصورة',
                          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: Colors.white,
                            fontWeight: FontWeight.w500,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                    ),
                  ],
                )
              : Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Container(
                      width: 80,
                      height: 80,
                      decoration: BoxDecoration(
                        color: AppColors.accent,
                        shape: BoxShape.circle,
                        boxShadow: [
                          BoxShadow(
                            color: AppColors.accent.withValues(alpha: 0.3),
                            blurRadius: 20,
                            offset: const Offset(0, 8),
                          ),
                        ],
                      ),
                      child: const Icon(
                        Icons.credit_card_rounded,
                        size: 40,
                        color: Colors.white,
                      ),
                    ),
                    const SizedBox(height: 20),
                    Text(
                      'رفع صورة رخصة القيادة',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        color: AppColors.accent,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'اضغط لاختيار صورة واضحة لرخصة القيادة',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: AppColors.textSecondary,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
        ),
      ),
    );
  }

  Widget _buildLicenseFields() {
    return Column(
      children: [
        CustomTextField(
          controller: _licenseNumberController,
          label: 'رقم رخصة القيادة (اختياري)',
          hint: 'أدخل رقم رخصة القيادة إذا أردت',
          prefixIcon: Icons.badge_outlined,
        ),
        const SizedBox(height: 20),
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: AppColors.accent.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: AppColors.accent.withValues(alpha: 0.3),
            ),
          ),
          child: Row(
            children: [
              Icon(
                Icons.info_outline,
                color: AppColors.accent,
                size: 20,
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  'يكفي رفع صورة واضحة لرخصة القيادة للمتابعة',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: AppColors.accent,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  // Vehicle Step Components
  Widget _buildVehicleImageSection() {
    return Center(
      child: GestureDetector(
        onTap: _showImageSourceDialog,
        child: AnimatedContainer(
          duration: const Duration(milliseconds: 300),
          width: double.infinity,
          height: 240,
          decoration: BoxDecoration(
            gradient: _vehicleImage != null
                ? null
                : LinearGradient(
                    colors: [
                      AppColors.primary.withValues(alpha: 0.1),
                      AppColors.primary.withValues(alpha: 0.05),
                    ],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
            borderRadius: BorderRadius.circular(24),
            border: Border.all(
              color: _vehicleImage != null
                  ? AppColors.primary.withValues(alpha: 0.6)
                  : AppColors.primary.withValues(alpha: 0.3),
              width: 2,
            ),
            boxShadow: [
              BoxShadow(
                color: AppColors.primary.withValues(alpha: 0.15),
                blurRadius: 16,
                offset: const Offset(0, 6),
              ),
            ],
          ),
          child: _vehicleImage != null
              ? Stack(
                  children: [
                    ClipRRect(
                      borderRadius: BorderRadius.circular(22),
                      child: WebImagePreview(
                        imageFile: _vehicleImage,
                        width: double.infinity,
                        height: 240,
                        fit: BoxFit.cover,
                      ),
                    ),
                    // Success indicator
                    Positioned(
                      top: 16,
                      right: 16,
                      child: Container(
                        padding: const EdgeInsets.all(10),
                        decoration: BoxDecoration(
                          color: AppColors.success,
                          shape: BoxShape.circle,
                          boxShadow: [
                            BoxShadow(
                              color: AppColors.success.withValues(alpha: 0.4),
                              blurRadius: 12,
                              offset: const Offset(0, 4),
                            ),
                          ],
                        ),
                        child: const Icon(
                          Icons.check_rounded,
                          color: Colors.white,
                          size: 18,
                        ),
                      ),
                    ),
                    // Change image overlay
                    Positioned(
                      bottom: 16,
                      left: 16,
                      right: 16,
                      child: Container(
                        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                        decoration: BoxDecoration(
                          color: Colors.black.withValues(alpha: 0.75),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            const Icon(
                              Icons.edit_rounded,
                              color: Colors.white,
                              size: 16,
                            ),
                            const SizedBox(width: 8),
                            Text(
                              'اضغط لتغيير صورة المركبة',
                              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                color: Colors.white,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                )
              : Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Container(
                      width: 100,
                      height: 100,
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          colors: [
                            AppColors.primary,
                            AppColors.primary.withValues(alpha: 0.8),
                          ],
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                        ),
                        shape: BoxShape.circle,
                        boxShadow: [
                          BoxShadow(
                            color: AppColors.primary.withValues(alpha: 0.3),
                            blurRadius: 24,
                            offset: const Offset(0, 12),
                          ),
                        ],
                      ),
                      child: const Icon(
                        Icons.directions_car_rounded,
                        size: 50,
                        color: Colors.white,
                      ),
                    ),
                    const SizedBox(height: 24),
                    Text(
                      'صورة المركبة',
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        color: AppColors.primary,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'اضغط لرفع صورة واضحة للمركبة من الخارج',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: AppColors.textSecondary,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
        ),
      ),
    );
  }

  Widget _buildVehicleFields() {
    return Column(
      children: [
        // Enhanced form with better aesthetics
        Container(
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: AppColors.surface,
            borderRadius: BorderRadius.circular(20),
            boxShadow: [
              BoxShadow(
                color: AppColors.shadow.withValues(alpha: 0.1),
                blurRadius: 12,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Column(
            children: [
              // Make and Model row
              Row(
                children: [
                  Expanded(
                    child: CustomTextField(
                      controller: _vehicleMakeController,
                      label: 'الماركة',
                      hint: 'Toyota, تويوتا, BMW...',
                      prefixIcon: Icons.branding_watermark_outlined,
                      onChanged: (value) => setState(() {}),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: CustomTextField(
                      controller: _vehicleModelController,
                      label: 'الموديل',
                      hint: 'Camry, كامري, X5...',
                      prefixIcon: Icons.car_rental_outlined,
                      onChanged: (value) => setState(() {}),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 20),
              // Color and Plate row
              Row(
                children: [
                  Expanded(
                    child: CustomTextField(
                      controller: _vehicleColorController,
                      label: 'اللون',
                      hint: 'أبيض, White, أزرق...',
                      prefixIcon: Icons.palette_outlined,
                      onChanged: (value) => setState(() {}),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: CustomTextField(
                      controller: _vehiclePlateController,
                      label: 'رقم اللوحة',
                      hint: 'أ ب ج 1234, ABC 123...',
                      prefixIcon: Icons.confirmation_number_outlined,
                      onChanged: (value) => setState(() {}),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 20),
              // Seats field (centered)
              Row(
                children: [
                  Expanded(
                    child: CustomTextField(
                      controller: _vehicleSeatsController,
                      label: 'عدد المقاعد',
                      hint: '4, 5, 7...',
                      prefixIcon: Icons.airline_seat_recline_normal_outlined,
                      keyboardType: TextInputType.number,
                      onChanged: (value) => setState(() {}),
                    ),
                  ),
                  const Expanded(child: SizedBox()), // Empty space for balance
                ],
              ),
            ],
          ),
        ),
        const SizedBox(height: 20),
        // Info message about flexible input
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: AppColors.primary.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: AppColors.primary.withValues(alpha: 0.3),
            ),
          ),
          child: Row(
            children: [
              Icon(
                Icons.info_outline,
                color: AppColors.primary,
                size: 20,
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  'يمكنك الكتابة بالعربية أو الإنجليزية أو الأرقام في جميع الحقول',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: AppColors.primary,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  // Payment Step Components
  Widget _buildPaymentSection() {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: AppColors.shadow,
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        children: [
          Icon(
            Icons.payment_rounded,
            size: 64,
            color: AppColors.success,
          ),
          const SizedBox(height: 20),
          Text(
            'شحن الحساب',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
              color: AppColors.success,
            ),
          ),
          const SizedBox(height: 12),
          Text(
            'اشحن حسابك بـ 20 درهم لضمان الجدية والبدء في تقديم الخدمة',
            textAlign: TextAlign.center,
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
              color: AppColors.textSecondary,
              height: 1.5,
            ),
          ),
          const SizedBox(height: 24),
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: AppColors.success.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: AppColors.success.withValues(alpha: 0.3),
              ),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.info_outline,
                  color: AppColors.success,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    'هذا المبلغ قابل للاسترداد ويضمن جدية السائقين',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: AppColors.success,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // Navigation Components
  Widget _buildNavigationButtons() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: AppColors.surface,
        boxShadow: [
          BoxShadow(
            color: AppColors.shadow,
            blurRadius: 8,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Row(
        children: [
          if (_currentStep > 0)
            Expanded(
              child: CustomButton(
                text: 'السابق',
                onPressed: _previousStep,
                isOutlined: true,
                icon: Icons.arrow_back_rounded,
              ),
            ),
          if (_currentStep > 0) const SizedBox(width: 16),
          Expanded(
            child: CustomButton(
              text: _getNextButtonText(),
              onPressed: _isLoading ? null : _canProceed() ? _nextStep : null,
              isLoading: _isLoading,
              icon: _currentStep == 3 ? Icons.check_rounded : Icons.arrow_forward_rounded,
            ),
          ),
        ],
      ),
    );
  }

  String _getNextButtonText() {
    switch (_currentStep) {
      case 0:
        return 'التالي';
      case 1:
        return 'التالي';
      case 2:
        return 'التالي';
      case 3:
        return 'إكمال التفعيل';
      default:
        return 'التالي';
    }
  }

  bool _canProceed() {
    switch (_currentStep) {
      case 0:
        return _nameController.text.trim().length >= 4 && _profileImage != null;
      case 1:
        return _licenseImage != null;
      case 2:
        return _vehicleMakeController.text.trim().isNotEmpty &&
               _vehicleModelController.text.trim().isNotEmpty &&
               _vehicleColorController.text.trim().isNotEmpty &&
               _vehiclePlateController.text.trim().isNotEmpty &&
               _vehicleSeatsController.text.trim().isNotEmpty &&
               _vehicleImage != null;
      case 3:
        return true; // Payment step is always ready
      default:
        return false;
    }
  }

  void _previousStep() {
    if (_currentStep > 0) {
      _pageController.previousPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  void _nextStep() async {
    if (_currentStep < 3) {
      _pageController.nextPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    } else {
      // Complete activation
      await _completeActivation();
    }
  }

  Future<void> _completeActivation() async {
    setState(() => _isLoading = true);

    try {
      final authProvider = provider.Provider.of<AuthProvider>(context, listen: false);
      final currentUser = authProvider.currentUser;

      if (currentUser == null) {
        _showErrorSnackBar('خطأ: لم يتم العثور على بيانات المستخدم');
        setState(() => _isLoading = false);
        return;
      }

      // Step 1: Save Personal Information
      await _savePersonalInfo(currentUser, authProvider);

      // Step 2: Save Driver License
      await _saveDriverLicense(currentUser.id);

      // Step 3: Save Vehicle Information
      await _saveVehicleInfo(currentUser.id);

      // Step 4: Process Payment (simulate for now)
      await _processPayment(currentUser.id);

      // Update user as activated driver (using isLeader field)
      final updatedUser = currentUser.copyWith(
        isLeader: true,
        role: 'trip_leader',
      );

      await authProvider.updateProfile(updatedUser);

      _showSuccessSnackBar('تم تفعيل وضع السائق بنجاح!');

      if (mounted) {
        Navigator.pop(context);
      }
    } catch (e) {
      _showErrorSnackBar('فشل في تفعيل وضع السائق: ${e.toString()}');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _savePersonalInfo(UserModel currentUser, AuthProvider authProvider) async {
    String? profileImageUrl;

    // Upload profile image if selected
    if (_profileImage != null) {
      profileImageUrl = await StorageService.uploadProfileImage(
        imageFile: _profileImage!,
        userId: currentUser.id,
      );

      if (profileImageUrl == null) {
        throw Exception('فشل في رفع الصورة الشخصية');
      }
    }

    // Update user profile with new information
    final updatedUser = currentUser.copyWith(
      fullName: _nameController.text.trim(),
      profileImageUrl: profileImageUrl ?? currentUser.profileImageUrl,
    );

    final success = await authProvider.updateProfile(updatedUser);
    if (!success) {
      throw Exception('فشل في حفظ المعلومات الشخصية');
    }
  }

  Future<void> _saveDriverLicense(String userId) async {
    if (_licenseImage == null) {
      throw Exception('يرجى رفع صورة رخصة القيادة');
    }

    // Upload license image to private driver-licenses bucket
    final licenseImagePath = await StorageService.uploadDriverLicense(
      imageFile: _licenseImage!,
      userId: userId,
    );

    if (licenseImagePath == null) {
      throw Exception('فشل في رفع صورة رخصة القيادة');
    }

    // Save license data to Supabase
    try {
      final supabase = Supabase.instance.client;

      // Create or update driver_licenses table entry
      await supabase.from('driver_licenses').upsert({
        'user_id': userId,
        'license_number': _licenseNumberController.text.trim().isEmpty
            ? null
            : _licenseNumberController.text.trim(),
        'license_image_path': licenseImagePath, // Store path, not URL
        'verified': false,
        'created_at': DateTime.now().toIso8601String(),
        'updated_at': DateTime.now().toIso8601String(),
      });

      if (kDebugMode) {
        print('License saved successfully to private bucket: $licenseImagePath');
      }
    } catch (e) {
      throw Exception('فشل في حفظ بيانات رخصة القيادة: ${e.toString()}');
    }
  }

  Future<void> _saveVehicleInfo(String userId) async {
    if (_vehicleImage == null) {
      throw Exception('يرجى رفع صورة المركبة');
    }

    // Upload vehicle image to public profile-images bucket
    final vehicleImageUrl = await StorageService.uploadCarImage(
      imageFile: _vehicleImage!,
      userId: userId,
    );

    if (vehicleImageUrl == null) {
      throw Exception('فشل في رفع صورة المركبة');
    }

    // Save vehicle data to Supabase
    try {
      final supabase = Supabase.instance.client;

      // Create or update vehicles table entry
      await supabase.from('vehicles').upsert({
        'owner_id': userId,
        'make': _vehicleMakeController.text.trim(),
        'model': _vehicleModelController.text.trim(),
        'color': _vehicleColorController.text.trim(),
        'plate_number': _vehiclePlateController.text.trim(),
        'seats': int.tryParse(_vehicleSeatsController.text.trim()) ?? 4,
        'image_url': vehicleImageUrl,
        'created_at': DateTime.now().toIso8601String(),
        'updated_at': DateTime.now().toIso8601String(),
      });

      if (kDebugMode) {
        print('Vehicle saved successfully to public bucket: $vehicleImageUrl');
      }
    } catch (e) {
      throw Exception('فشل في حفظ بيانات المركبة: ${e.toString()}');
    }
  }

  Future<void> _processPayment(String userId) async {
    // TODO: Implement actual payment processing
    // For now, just simulate payment
    await Future.delayed(const Duration(seconds: 1));
    print('Payment processed for user: $userId');
  }
}
