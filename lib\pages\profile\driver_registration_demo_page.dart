import 'package:flutter/material.dart';
import '../../constants/app_theme.dart';
import '../../widgets/custom_button.dart';
import 'driver_name_and_avatar_page.dart';

class DriverRegistrationDemoPage extends StatelessWidget {
  const DriverRegistrationDemoPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: IconButton(
          onPressed: () => Navigator.pop(context),
          icon: const Icon(
            Icons.arrow_back_ios_rounded,
            color: AppColors.textPrimary,
          ),
        ),
        title: Text(
          'تسجيل السائق',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
            color: AppColors.textPrimary,
          ),
        ),
        centerTitle: true,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            _buildHeader(context),
            const SizedBox(height: 32),
            _buildRequirementsList(context),
            const SizedBox(height: 40),
            _buildStartButton(context),
            const SizedBox(height: 20),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Column(
      children: [
        Container(
          width: 100,
          height: 100,
          decoration: BoxDecoration(
            color: AppColors.primary.withValues(alpha: 0.1),
            shape: BoxShape.circle,
          ),
          child: Icon(
            Icons.drive_eta_rounded,
            size: 50,
            color: AppColors.primary,
          ),
        ),
        const SizedBox(height: 24),
        Text(
          'انضم كسائق',
          style: Theme.of(context).textTheme.headlineMedium?.copyWith(
            fontWeight: FontWeight.bold,
            color: AppColors.primary,
          ),
        ),
        const SizedBox(height: 12),
        Text(
          'ابدأ رحلتك كسائق معتمد واربح المال من خلال توفير رحلات آمنة ومريحة',
          textAlign: TextAlign.center,
          style: Theme.of(context).textTheme.bodyLarge?.copyWith(
            color: AppColors.textSecondary,
            height: 1.6,
          ),
        ),
      ],
    );
  }

  Widget _buildRequirementsList(BuildContext context) {
    final requirements = [
      {
        'icon': Icons.person_rounded,
        'title': 'الاسم الكامل والصورة الشخصية',
        'description': 'أدخل اسمك الكامل وارفع صورة شخصية واضحة',
        'status': 'current',
      },
      {
        'icon': Icons.credit_card_rounded,
        'title': 'صورة رخصة القيادة',
        'description': 'ارفع صورة واضحة لرخصة القيادة السارية',
        'status': 'pending',
      },
      {
        'icon': Icons.directions_car_rounded,
        'title': 'معلومات المركبة',
        'description': 'أدخل تفاصيل سيارتك وارفع صورها',
        'status': 'pending',
      },
      {
        'icon': Icons.payment_rounded,
        'title': 'رسوم التسجيل (20 درهم)',
        'description': 'ادفع رسوم التسجيل لتفعيل حسابك كسائق',
        'status': 'pending',
      },
    ];

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: AppColors.shadow,
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'متطلبات التسجيل',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
              color: AppColors.textPrimary,
            ),
          ),
          const SizedBox(height: 20),
          ...requirements.map((req) => _buildRequirementItem(
            context,
            req['icon'] as IconData,
            req['title'] as String,
            req['description'] as String,
            req['status'] as String,
          )),
        ],
      ),
    );
  }

  Widget _buildRequirementItem(
    BuildContext context,
    IconData icon,
    String title,
    String description,
    String status,
  ) {
    final isActive = status == 'current';
    final isCompleted = status == 'completed';
    
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: isActive 
            ? AppColors.primary.withValues(alpha: 0.1)
            : AppColors.surfaceVariant.withValues(alpha: 0.5),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isActive 
              ? AppColors.primary.withValues(alpha: 0.3)
              : AppColors.border.withValues(alpha: 0.3),
        ),
      ),
      child: Row(
        children: [
          Container(
            width: 48,
            height: 48,
            decoration: BoxDecoration(
              color: isActive 
                  ? AppColors.primary
                  : isCompleted 
                      ? AppColors.success
                      : AppColors.textTertiary,
              shape: BoxShape.circle,
            ),
            child: Icon(
              isCompleted ? Icons.check_rounded : icon,
              color: Colors.white,
              size: 24,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: isActive ? AppColors.primary : AppColors.textPrimary,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  description,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: AppColors.textSecondary,
                    height: 1.4,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStartButton(BuildContext context) {
    return CustomButton(
      text: 'ابدأ التسجيل',
      onPressed: () {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => const DriverNameAndAvatarPage(),
          ),
        );
      },
      icon: Icons.arrow_forward_rounded,
      width: double.infinity,
      height: 56,
    );
  }
}
